---
import '../styles/global.css';
import Header from '@/components/Header.astro';
import Hero from '@/components/Hero.astro';
import Publications from '@/components/Publications.astro';
import Experience from '@/components/Experience.astro';
import SoftwareScreens from '@/components/SoftwareScreens.astro';
import Footer from '@/components/Footer.astro';
import {
  personalInfo,
  researchFields,
  publications,
  employment,
  education,
  certifications,
  softwareScreens,
  navigation,
  siteInfo
} from '@/data/portfolio';
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{siteInfo.title}</title>
  </head>

  <body class="bg-[#f3f6fb] text-slate-800">
    <Header
      brandName={siteInfo.brandName}
      navigation={navigation}
      skipToContentText={siteInfo.skipToContentText}
    />

    <main id="main" class="mx-auto max-w-6xl px-4 sm:px-6">
      <Hero
        personalInfo={personalInfo}
        researchFields={researchFields}
      />

      <Publications publications={publications} />

      <Experience
        employment={employment}
        education={education}
        certifications={certifications}
      />

      <SoftwareScreens softwareScreens={softwareScreens} />
    </main>

    <Footer
      personalName={personalInfo.name}
      footerText={siteInfo.footerText}
    />
  </body>
</html>
