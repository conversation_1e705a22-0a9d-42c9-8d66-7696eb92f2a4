---
import '../styles/global.css';
import { Image, Font } from 'astro:assets';
import profilePlaceholder from '@/assets/images/profile-placeholder.svg';
import publicationThumb from '@/assets/images/publication-thumb.svg';
import softwareScreenshot from '@/assets/images/software-screenshot.svg';
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title><PERSON><PERSON><PERSON> Cong <PERSON>h // Portfolio</title>
  </head>

  <body class="bg-[#f3f6fb] text-slate-800">
    <a class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 bg-white border border-slate-300 p-2 rounded" href="#main">Skip to content</a>

    <!-- Header -->
    <header class="sticky top-0 z-40 backdrop-blur supports-[backdrop-filter]:bg-white/70 bg-white/90 border-b border-slate-200">
      <nav class="mx-auto max-w-6xl px-4 sm:px-6 py-3 flex items-center justify-between" aria-label="Main navigation">
        <div class="text-sky-900 font-bold tracking-tight">N. C. Thanh // Portfolio</div>
        <ul class="flex gap-6 text-sm" role="list">
          <li><a href="#home" class="hover:text-sky-800 focus:outline-none focus:ring-2 focus:ring-sky-600 rounded">Home</a></li>
          <li><a href="#publications" class="hover:text-sky-800 focus:outline-none focus:ring-2 focus:ring-sky-600 rounded">Publications</a></li>
          <li><a href="#experience" class="hover:text-sky-800 focus:outline-none focus:ring-2 focus:ring-sky-600 rounded">Experience</a></li>
          <li><a href="#screens" class="hover:text-sky-800 focus:outline-none focus:ring-2 focus:ring-sky-600 rounded">Software Screens</a></li>
        </ul>
      </nav>
    </header>

    <main id="main" class="mx-auto max-w-6xl px-4 sm:px-6">
      <!-- Hero Section -->
      <section id="home" class="pt-10 sm:pt-14 pb-10">
        <div class="grid grid-cols-1 md:grid-cols-[260px,1fr] gap-8 items-start">
          <figure class="mx-auto md:mx-0 w-[220px] h-[220px] md:w-[240px] md:h-[240px] rounded-full ring-4 ring-sky-800/80 ring-offset-4 ring-offset-slate-100 overflow-hidden bg-slate-100 flex items-center justify-center">
            <Image src={profilePlaceholder} alt="Profile picture placeholder for Nguyen Cong Thanh" width={240} height={240} class="object-cover" />
          </figure>

          <div>
            <h1 class="text-3xl sm:text-4xl text-sky-900 font-extrabold">Nguyen Cong Thanh</h1>
            <p class="mt-1 text-slate-600 italic">[Current Job Title – e.g., Data Scientist, Research Fellow]</p>

            <dl class="mt-5 grid grid-cols-1 sm:grid-cols-2 gap-3" aria-label="Professional contact information">
              <div class="bg-white border border-slate-200 rounded px-3 py-2">
                <dt class="sr-only">Email</dt>
                <dd class="text-sm">Email: <EMAIL></dd>
              </div>
              <div class="bg-white border border-slate-200 rounded px-3 py-2">
                <dt class="sr-only">Phone</dt>
                <dd class="text-sm">Phone: [+84 xxx xxx xxx]</dd>
              </div>
              <div class="bg-white border border-slate-200 rounded px-3 py-2 sm:col-span-2">
                <dt class="sr-only">Address</dt>
                <dd class="text-sm">Address: [City, Country] • [Institution or Organization]</dd>
              </div>
            </dl>

            <div class="mt-4">
              <h2 class="text-sky-900 font-bold">Research Fields</h2>
              <ul class="mt-2 flex flex-wrap gap-2" role="list" aria-label="Research specialization tags">
                <li><span class="inline-block bg-slate-50 text-sky-900 border border-slate-300 px-3 py-1 rounded-full text-xs">Causal Inference</span></li>
                <li><span class="inline-block bg-slate-50 text-sky-900 border border-slate-300 px-3 py-1 rounded-full text-xs">Machine Learning</span></li>
                <li><span class="inline-block bg-slate-50 text-sky-900 border border-slate-300 px-3 py-1 rounded-full text-xs">Econometrics</span></li>
                <li><span class="inline-block bg-slate-50 text-sky-900 border border-slate-300 px-3 py-1 rounded-full text-xs">Bayesian Modeling</span></li>
                <li><span class="inline-block bg-slate-50 text-sky-900 border border-slate-300 px-3 py-1 rounded-full text-xs">Policy Evaluation</span></li>
                <li><span class="inline-block bg-slate-50 text-sky-900 border border-slate-300 px-3 py-1 rounded-full text-xs">R / Stata / Python</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- Publications Section -->
      <section id="publications" aria-labelledby="pub-title" class="py-10 border-t border-slate-200">
        <div class="flex items-center justify-between">
          <h2 id="pub-title" class="text-2xl text-sky-900 font-extrabold">Academic Peer-Reviewed Publications</h2>
        </div>

        <!-- Published -->
        <div class="mt-6">
          <h3 class="text-sky-900 font-bold">Published Papers</h3>
          <ol class="relative mt-4 space-y-6" role="list">
            {Array.from({ length: 3 }).map((_, i) => (
              <li class="pl-14" aria-label={`Publication ${i + 1}`}>
                <div class="absolute left-0 top-0 flex items-center" aria-hidden="true">
                  <div class="w-10 h-10 rounded-full bg-sky-100 border border-sky-300 text-sky-900 grid place-items-center font-bold">{String(i + 1).padStart(2, '0')}</div>
                </div>
                <article class="grid grid-cols-1 md:grid-cols-[120px,1fr] gap-4 bg-white border border-slate-200 rounded p-4">
                  <div class="w-full h-[90px] md:h-[96px] overflow-hidden rounded bg-slate-50 grid place-items-center">
                    <Image src={publicationThumb} width={120} height={90} alt="Publication thumbnail placeholder" />
                  </div>
                  <div>
                    <header>
                      <p class="text-xs text-slate-500">Nguyen Cong Thanh, A. Researcher — {2025 - i}</p>
                      <h4 class="text-sky-900 font-semibold">Paper Title Placeholder {i + 1}</h4>
                      <p class="text-sm">Journal of Statistical Research</p>
                    </header>
                    <p class="mt-2 text-xs text-slate-600">Notes: Open data and code available upon request.</p>
                  </div>
                </article>
              </li>
            ))}
          </ol>
        </div>

        <!-- Unpublished -->
        <div class="mt-10">
          <h3 class="text-sky-900 font-bold">Unpublished Papers</h3>
          <ol class="relative mt-4 space-y-6" role="list">
            {Array.from({ length: 2 }).map((_, i) => (
              <li class="pl-14" aria-label={`Working paper ${i + 1}`}>
                <div class="absolute left-0 top-0 flex items-center" aria-hidden="true">
                  <div class="w-10 h-10 rounded-full bg-amber-50 border border-amber-300 text-amber-900 grid place-items-center font-bold">{String(i + 1).padStart(2, '0')}</div>
                </div>
                <article class="grid grid-cols-1 md:grid-cols-[120px,1fr] gap-4 bg-white border border-slate-200 rounded p-4">
                  <div class="w-full h-[90px] md:h-[96px] overflow-hidden rounded bg-slate-50 grid place-items-center">
                    <Image src={publicationThumb} width={120} height={90} alt="Working paper thumbnail placeholder" />
                  </div>
                  <div>
                    <header>
                      <p class="text-xs text-slate-500">Nguyen Cong Thanh, E. Collaborator — {2024 - i}</p>
                      <h4 class="text-sky-900 font-semibold">Working Paper Title Placeholder {i + 1}</h4>
                      <p class="text-sm">Manuscript</p>
                    </header>
                    <p class="mt-2 text-xs text-slate-600">Notes: Preprint available upon request.</p>
                  </div>
                </article>
              </li>
            ))}
          </ol>
        </div>
      </section>

      <!-- Experience Section -->
      <section id="experience" aria-labelledby="exp-title" class="py-10 border-t border-slate-200">
        <h2 id="exp-title" class="text-2xl text-sky-900 font-extrabold">Professional Experience</h2>

        <div class="mt-6 space-y-8">
          <!-- Employment -->
          <section aria-labelledby="employment-title">
            <h3 id="employment-title" class="text-sky-900 font-bold">Employment Experience</h3>
            <div class="mt-3 grid gap-4">
              <article class="bg-white border border-slate-200 rounded p-4">
                <h4 class="text-sky-900 font-semibold">Data Scientist</h4>
                <p class="text-sm text-slate-600">[Company / Lab Name] — 2023–Present</p>
                <ul class="list-disc pl-5 mt-2 text-sm space-y-1">
                  <li>Lead econometric modeling and causal inference for policy analysis.</li>
                  <li>Designed end-to-end ML pipelines in Python and Stata.</li>
                  <li>Collaborated with academia and stakeholders to translate findings.</li>
                </ul>
              </article>
              <article class="bg-white border border-slate-200 rounded p-4">
                <h4 class="text-sky-900 font-semibold">Research Assistant</h4>
                <p class="text-sm text-slate-600">[University / Institute] — 2021–2023</p>
                <ul class="list-disc pl-5 mt-2 text-sm space-y-1">
                  <li>Implemented Bayesian hierarchical models for panel data.</li>
                  <li>Co-authored publications and conference presentations.</li>
                </ul>
              </article>
            </div>
          </section>

          <!-- Degrees -->
          <section aria-labelledby="degrees-title" class="border-t border-slate-100 pt-6">
            <h3 id="degrees-title" class="text-sky-900 font-bold">Academic Qualifications</h3>
            <div class="mt-3 grid gap-4">
              <article class="bg-white border border-slate-200 rounded p-4">
                <h4 class="text-sky-900 font-semibold">Ph.D., Econometrics / Data Science</h4>
                <p class="text-sm text-slate-600">[University Name] — 2018–2022</p>
                <ul class="list-disc pl-5 mt-2 text-sm space-y-1">
                  <li>Dissertation: Robust Methods for Causal Effect Estimation.</li>
                  <li>Teaching: Statistical Inference, Econometrics I.</li>
                </ul>
              </article>
              <article class="bg-white border border-slate-200 rounded p-4">
                <h4 class="text-sky-900 font-semibold">M.Sc., Statistics</h4>
                <p class="text-sm text-slate-600">[University Name] — 2016–2018</p>
              </article>
              <article class="bg-white border border-slate-200 rounded p-4">
                <h4 class="text-sky-900 font-semibold">B.Sc., Mathematics</h4>
                <p class="text-sm text-slate-600">[University Name] — 2012–2016</p>
              </article>
            </div>
          </section>

          <!-- Certifications -->
          <section aria-labelledby="certs-title" class="border-t border-slate-100 pt-6">
            <h3 id="certs-title" class="text-sky-900 font-bold">Professional Certifications</h3>
            <div class="mt-3 grid gap-4">
              <article class="bg-white border border-slate-200 rounded p-4">
                <h4 class="text-sky-900 font-semibold">Certified Data Scientist</h4>
                <p class="text-sm text-slate-600">[Issuing Organization] — Issued 2024</p>
              </article>
            </div>
          </section>
        </div>
      </section>

      <!-- Software Screens Section -->
      <section id="screens" class="py-10 border-t border-slate-200">
        <h2 class="text-2xl text-sky-900 font-extrabold">Software Screens</h2>
        <p class="mt-2 text-sm text-slate-600">Placeholders for statistical software interfaces (Stata, SPSS).</p>
        <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
          <figure class="bg-white border border-slate-200 rounded p-2">
            <Image src={softwareScreenshot} alt="Placeholder for Stata interface screenshot" width={800} height={480} class="w-full h-auto" />
            <figcaption class="text-xs text-slate-600 mt-2">Stata interface placeholder</figcaption>
          </figure>
          <figure class="bg-white border border-slate-200 rounded p-2">
            <Image src={softwareScreenshot} alt="Placeholder for SPSS interface screenshot" width={800} height={480} class="w-full h-auto" />
            <figcaption class="text-xs text-slate-600 mt-2">SPSS interface placeholder</figcaption>
          </figure>
        </div>
      </section>
    </main>

    <footer class="mt-12 py-8 text-center text-xs text-slate-500 border-t border-slate-200">© {new Date().getFullYear()} Nguyen Cong Thanh — Academic Portfolio</footer>
  </body>
</html>
